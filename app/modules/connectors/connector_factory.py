"""
Connector Factory

This module implements the factory pattern for dynamically loading and instantiating
connectors based on their source type. It provides a centralized way to manage
all available connectors and their configurations.
"""

import logging
import importlib
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

from app.modules.connectors.base import BaseConnector
from app.services.neo4j_service import execute_read_query

logger = logging.getLogger(__name__)


class ConnectorFactory:
    """
    Factory class for creating connector instances.
    
    This factory dynamically loads connector classes and provides methods
    to instantiate them with appropriate configurations.
    """
    
    def __init__(self):
        """Initialize the connector factory."""
        self._connector_classes: Dict[str, Type[BaseConnector]] = {}
        self._connector_metadata: Dict[str, Dict[str, Any]] = {}
        self._initialized = False
        
    def initialize(self):
        """
        Initialize the factory by loading all active connectors from the database.
        
        This method queries the database for active connectors and dynamically
        imports their classes.
        """
        if self._initialized:
            return
            
        try:
            logger.info("Initializing connector factory...")
            
            # Query for active connectors from database
            active_connectors = self._get_active_connectors()
            
            # Load each active connector
            for connector_info in active_connectors:
                self._load_connector(connector_info)
                
            self._initialized = True
            logger.info(f"Connector factory initialized with {len(self._connector_classes)} connectors")
            
        except Exception as e:
            logger.error(f"Failed to initialize connector factory: {str(e)}")
            raise
    
    def get_connector(self, source_type: str, config: Optional[Dict[str, Any]] = None) -> BaseConnector:
        """
        Get a connector instance for the specified source type.
        
        Args:
            source_type: The type of connector to create (e.g., 'gdrive', 'github')
            config: Configuration dictionary for the connector
            
        Returns:
            BaseConnector: Configured connector instance
            
        Raises:
            ValueError: If source_type is not supported
            Exception: If connector instantiation fails
        """
        if not self._initialized:
            self.initialize()
            
        if source_type not in self._connector_classes:
            available = list(self._connector_classes.keys())
            raise ValueError(f"Unsupported connector type '{source_type}'. Available: {available}")
        
        try:
            connector_class = self._connector_classes[source_type]
            connector = connector_class(config)
            
            logger.debug(f"Created connector instance for {source_type}")
            return connector
            
        except Exception as e:
            logger.error(f"Failed to create connector for {source_type}: {str(e)}")
            raise
    
    def get_available_connectors(self) -> List[Dict[str, Any]]:
        """
        Get list of all available connectors.
        
        Returns:
            List[Dict[str, Any]]: List of connector metadata
        """
        if not self._initialized:
            self.initialize()
            
        return list(self._connector_metadata.values())
    
    def is_connector_available(self, source_type: str) -> bool:
        """
        Check if a connector is available.
        
        Args:
            source_type: The connector source type to check
            
        Returns:
            bool: True if connector is available, False otherwise
        """
        if not self._initialized:
            self.initialize()
            
        return source_type in self._connector_classes
    
    def get_connector_metadata(self, source_type: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific connector.
        
        Args:
            source_type: The connector source type
            
        Returns:
            Optional[Dict[str, Any]]: Connector metadata or None if not found
        """
        if not self._initialized:
            self.initialize()
            
        return self._connector_metadata.get(source_type)
    
    def reload_connector(self, source_type: str):
        """
        Reload a specific connector (useful for development).
        
        Args:
            source_type: The connector source type to reload
        """
        try:
            # Get connector info from database
            connector_info = self._get_connector_info(source_type)
            if connector_info:
                # Remove existing connector
                if source_type in self._connector_classes:
                    del self._connector_classes[source_type]
                if source_type in self._connector_metadata:
                    del self._connector_metadata[source_type]
                
                # Reload the connector
                self._load_connector(connector_info)
                logger.info(f"Reloaded connector: {source_type}")
            else:
                logger.warning(f"Connector not found in database: {source_type}")
                
        except Exception as e:
            logger.error(f"Failed to reload connector {source_type}: {str(e)}")
            raise
    
    def _get_active_connectors(self) -> List[Dict[str, Any]]:
        """
        Get active connectors from the database.
        
        Returns:
            List[Dict[str, Any]]: List of active connector information
        """
        try:
            # This would typically query a connectors table in PostgreSQL
            # For now, we'll return the known Google Drive connector
            # TODO: Replace with actual database query when connector registry is implemented
            
            return [
                {
                    'source_type': 'gdrive',
                    'name': 'Google Drive',
                    'connector_type': 'unstructured',
                    'module_path': 'app.modules.connectors.handlers.gdrive.service',
                    'class_name': 'GoogleDriveConnectorService',
                    'is_active': True,
                    'version': '1.0.0'
                },
                {
                    'source_type': 'github',
                    'name': 'GitHub',
                    'connector_type': 'code_repository',
                    'module_path': 'app.modules.connectors.handlers.github.service',
                    'class_name': 'GitHubConnector',
                    'is_active': True,
                    'version': '1.0.0'
                }
            ]
            
        except Exception as e:
            logger.error(f"Failed to get active connectors: {str(e)}")
            return []
    
    def _get_connector_info(self, source_type: str) -> Optional[Dict[str, Any]]:
        """
        Get connector information from database.
        
        Args:
            source_type: The connector source type
            
        Returns:
            Optional[Dict[str, Any]]: Connector information or None if not found
        """
        try:
            # TODO: Replace with actual database query
            active_connectors = self._get_active_connectors()
            for connector in active_connectors:
                if connector['source_type'] == source_type:
                    return connector
            return None
            
        except Exception as e:
            logger.error(f"Failed to get connector info for {source_type}: {str(e)}")
            return None
    
    def _load_connector(self, connector_info: Dict[str, Any]):
        """
        Load a connector class dynamically.
        
        Args:
            connector_info: Connector information from database
        """
        source_type = connector_info['source_type']
        
        try:
            # Construct module path and class name
            module_path = connector_info.get('module_path')
            class_name = connector_info.get('class_name')
            
            if not module_path or not class_name:
                # Use default naming convention
                module_path = f"app.modules.connectors.handlers.{source_type}.service"
                class_name = f"{source_type.title()}ConnectorService"
            
            # Import the module
            module = importlib.import_module(module_path)
            
            # Get the connector class
            connector_class = getattr(module, class_name)
            
            # Verify it's a BaseConnector subclass
            if not issubclass(connector_class, BaseConnector):
                raise ValueError(f"Class {class_name} is not a BaseConnector subclass")
            
            # Store the class and metadata
            self._connector_classes[source_type] = connector_class
            self._connector_metadata[source_type] = {
                'source_type': source_type,
                'name': connector_info.get('name', source_type.title()),
                'connector_type': connector_info.get('connector_type', 'unknown'),
                'version': connector_info.get('version', '1.0.0'),
                'is_active': connector_info.get('is_active', True),
                'loaded_at': datetime.now().isoformat(),
                'module_path': module_path,
                'class_name': class_name
            }
            
            logger.info(f"Loaded connector: {source_type} ({class_name})")
            
        except ImportError as e:
            logger.error(f"Failed to import connector module for {source_type}: {str(e)}")
            raise
        except AttributeError as e:
            logger.error(f"Connector class not found for {source_type}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Failed to load connector {source_type}: {str(e)}")
            raise
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get health status of the connector factory.
        
        Returns:
            Dict[str, Any]: Health status information
        """
        return {
            "initialized": self._initialized,
            "connectors_loaded": len(self._connector_classes),
            "available_connectors": list(self._connector_classes.keys()),
            "last_check": datetime.now().isoformat()
        }


# Global factory instance
connector_factory = ConnectorFactory()


def get_connector(source_type: str, config: Optional[Dict[str, Any]] = None) -> BaseConnector:
    """
    Convenience function to get a connector instance.
    
    Args:
        source_type: The type of connector to create
        config: Configuration dictionary for the connector
        
    Returns:
        BaseConnector: Configured connector instance
    """
    return connector_factory.get_connector(source_type, config)


def get_available_connectors() -> List[Dict[str, Any]]:
    """
    Convenience function to get all available connectors.
    
    Returns:
        List[Dict[str, Any]]: List of connector metadata
    """
    return connector_factory.get_available_connectors()


def is_connector_available(source_type: str) -> bool:
    """
    Convenience function to check if a connector is available.
    
    Args:
        source_type: The connector source type to check
        
    Returns:
        bool: True if connector is available, False otherwise
    """
    return connector_factory.is_connector_available(source_type)


def get_connector_factory() -> ConnectorFactory:
    """
    Get the global connector factory instance.
    
    Returns:
        ConnectorFactory: The global factory instance
    """
    return connector_factory