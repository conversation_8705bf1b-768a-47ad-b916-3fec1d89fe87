{"name": "GitHub", "description": "GitHub connector for repositories, issues, pull requests, and code", "version": "1.0.0", "type": "code_repository", "authentication": {"type": "personal_access_token", "required_fields": ["token"], "optional_fields": ["base_url"]}, "capabilities": ["repositories", "issues", "pull_requests", "commits", "code_search", "organizations", "users"], "data_types": ["repository", "issue", "pull_request", "commit", "file", "user", "organization"], "rate_limits": {"authenticated": 5000, "unauthenticated": 60}}