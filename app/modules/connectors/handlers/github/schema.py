"""
Pydantic schemas for GitHub connector data models.

These models are designed to capture the essential data for building a
knowledge graph, focusing on core identifiers, relationships, and context.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from enum import Enum

# --- Enums for core states ---


class GitHubUserType(str, Enum):
    USER = "User"
    ORGANIZATION = "Organization"
    BOT = "Bot"


class GitHubIssueState(str, Enum):
    OPEN = "open"
    CLOSED = "closed"


class GitHubPullRequestState(str, Enum):
    OPEN = "open"
    CLOSED = "closed"
    MERGED = "merged"


# --- Core Data Models ---


class GitHubUser(BaseModel):
    """A representation of a GitHub User or Organization."""

    id: int
    login: str
    node_id: str
    html_url: HttpUrl
    type: GitHubUserType
    name: Optional[str] = None
    email: Optional[str] = None
    created_at: Optional[datetime] = None


class GitHubRepository(BaseModel):
    """A representation of a GitHub Repository."""

    id: int
    node_id: str
    name: str
    full_name: str
    owner: GitHubUser  # Defines the OWNS_REPOSITORY relationship
    private: bool
    html_url: HttpUrl  # The direct web link to the repository
    description: Optional[str] = None
    fork: bool
    archived: bool
    language: Optional[str] = None
    stargazers_count: int
    watchers_count: int
    forks_count: int
    open_issues_count: int
    default_branch: str
    created_at: datetime
    updated_at: datetime
    pushed_at: Optional[datetime] = None
    license: Optional[str] = Field(
        None, description="Simplified license name, e.g., 'mit'"
    )


class GitHubIssue(BaseModel):
    """A representation of a GitHub Issue."""

    id: int
    node_id: str
    number: int
    html_url: HttpUrl  # The direct web link to the issue
    title: str
    user: GitHubUser  # Defines the CREATES_ISSUE relationship
    labels: List[str] = []  # Simplified from a list of objects
    state: GitHubIssueState
    assignees: List[GitHubUser] = []  # Defines the ASSIGNED_TO relationship
    body: Optional[str] = None
    comments: int
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime] = None
    closed_by: Optional[GitHubUser] = None  # Defines the CLOSES_ISSUE relationship


class GitHubBranchRef(BaseModel):
    """Represents the head or base of a PR for branch relationships."""

    ref: str  # The branch name
    sha: str
    repo: GitHubRepository


class GitHubPullRequest(BaseModel):
    """A representation of a GitHub Pull Request."""

    id: int
    node_id: str
    number: int
    html_url: HttpUrl  # The direct web link to the pull request
    state: GitHubPullRequestState
    title: str
    user: GitHubUser  # Defines the CREATES_PULL_REQUEST relationship
    body: Optional[str] = None
    assignees: List[GitHubUser] = []  # Defines the ASSIGNED_TO_PR relationship
    labels: List[str] = []
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime] = None
    merged_at: Optional[datetime] = None
    merged_by: Optional[GitHubUser] = (
        None  # Defines the MERGES_PULL_REQUEST relationship
    )
    head: GitHubBranchRef  # Source branch
    base: GitHubBranchRef  # Target branch
    author_association: str
    draft: bool
    additions: int
    deletions: int
    changed_files: int


class GitHubFileChange(BaseModel):
    """Represents a file changed within a commit."""

    filename: str
    status: str  # e.g., 'added', 'modified', 'removed'
    additions: int
    deletions: int
    patch: Optional[str] = None  # The diff patch, can be very useful context


class GitHubCommit(BaseModel):
    """A representation of a GitHub Commit."""

    sha: str
    node_id: str
    html_url: HttpUrl  # The direct web link to the commit
    author: Optional[GitHubUser]  # Defines the AUTHORS_COMMIT relationship
    committer: Optional[GitHubUser]
    message: str
    parents: List[str] = Field(
        ..., description="List of parent commit SHAs"
    )  # Defines PARENT_COMMIT
    additions: int
    deletions: int
    files: List[GitHubFileChange]  # Defines the MODIFIES_FILE relationship


class GitHubContent(BaseModel):
    """A representation of a file or directory's metadata in a repository."""

    type: str  # 'file' or 'dir'
    name: str
    path: str
    sha: str
    size: int
    html_url: Optional[HttpUrl] = None  # Direct web link to the file/dir in the repo
    download_url: Optional[HttpUrl] = None
    content: Optional[str] = Field(None, description="Base64 encoded content for files")
