import os
import yaml
from pathlib import Path

class GitHubSchema:
    def __init__(self, schema_path=None):
        # Default to module-relative path if no path provided
        if schema_path is None:
            schema_path = os.path.join(os.path.dirname(__file__), "github_schema.yml")
            
        self.schema = yaml.safe_load(Path(schema_path).read_text())
    
    def get_node_labels(self):
        return list(self.schema["nodes"].keys())
    
    def get_relationship_types(self):
        return list(self.schema["relationships"].keys())
    
    def validate_node_properties(self, label, properties):
        # Add validation logic here
        pass

# Initialize the schema
schema = GitHubSchema()
