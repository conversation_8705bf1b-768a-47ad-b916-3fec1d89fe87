# GitHub Connector Schema
version: 1.0
description: "Schema definition for GitHub connector entities and relationships"

nodes:
  GitHubRepository:
    description: "Represents a GitHub repository"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub repository ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this repository"
      node_id:
        type: string
        description: "GitHub node ID"
      name:
        type: string
        description: "Repository name"
      full_name:
        type: string
        description: "Full repository name (owner/repo)"
      html_url:
        type: string
        description: "Web URL for the repository"
      description:
        type: string
        description: "Repository description"
      private:
        type: boolean
        description: "Whether repository is private"
      fork:
        type: boolean
        description: "Whether repository is a fork"
      archived:
        type: boolean
        description: "Whether repository is archived"
      language:
        type: string
        description: "Primary programming language"
      stargazers_count:
        type: integer
        description: "Number of stars"
      watchers_count:
        type: integer
        description: "Number of watchers"
      forks_count:
        type: integer
        description: "Number of forks"
      open_issues_count:
        type: integer
        description: "Number of open issues"
      default_branch:
        type: string
        description: "Default branch name"
      created_at:
        type: timestamp
        description: "Repository creation timestamp"
      updated_at:
        type: timestamp
        description: "Repository last update timestamp"
      pushed_at:
        type: timestamp
        description: "Last push timestamp"
      license:
        type: string
        description: "Repository license"

  GitHubUser:
    description: "Represents a GitHub user"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub user ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation"
      node_id:
        type: string
        description: "GitHub node ID"
      login:
        type: string
        description: "GitHub username"
      name:
        type: string
        description: "User display name"
      email:
        type: string
        description: "User email address"
      html_url:
        type: string
        description: "User profile URL"
      avatar_url:
        type: string
        description: "User avatar URL"
      type:
        type: string
        description: "User type (User/Organization)"
      site_admin:
        type: boolean
        description: "Whether user is site admin"
      created_at:
        type: timestamp
        description: "User creation timestamp"
      updated_at:
        type: timestamp
        description: "User last update timestamp"

  GitHubIssue:
    description: "Represents a GitHub issue"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub issue ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation"
      node_id:
        type: string
        description: "GitHub node ID"
      number:
        type: integer
        description: "Issue number"
      html_url:
        type: string
        description: "Issue web URL"
      title:
        type: string
        description: "Issue title"
      body:
        type: string
        description: "Issue body content"
      state:
        type: string
        description: "Issue state (open/closed)"
      labels:
        type: string
        description: "JSON string of issue labels"
      comments:
        type: integer
        description: "Number of comments"
      created_at:
        type: timestamp
        description: "Issue creation timestamp"
      updated_at:
        type: timestamp
        description: "Issue last update timestamp"
      closed_at:
        type: timestamp
        description: "Issue close timestamp"

  GitHubPullRequest:
    description: "Represents a GitHub pull request"
    properties:
      id:
        type: integer
        required: true
        description: "GitHub pull request ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation"
      node_id:
        type: string
        description: "GitHub node ID"
      number:
        type: integer
        description: "Pull request number"
      html_url:
        type: string
        description: "Pull request web URL"
      title:
        type: string
        description: "Pull request title"
      body:
        type: string
        description: "Pull request body content"
      state:
        type: string
        description: "Pull request state"
      draft:
        type: boolean
        description: "Whether PR is draft"
      merged:
        type: boolean
        description: "Whether PR is merged"
      mergeable:
        type: boolean
        description: "Whether PR is mergeable"
      additions:
        type: integer
        description: "Number of additions"
      deletions:
        type: integer
        description: "Number of deletions"
      changed_files:
        type: integer
        description: "Number of changed files"
      created_at:
        type: timestamp
        description: "Pull request creation timestamp"
      updated_at:
        type: timestamp
        description: "Pull request last update timestamp"
      closed_at:
        type: timestamp
        description: "Pull request close timestamp"
      merged_at:
        type: timestamp
        description: "Pull request merge timestamp"

  GitHubCommit:
    description: "Represents a GitHub commit"
    properties:
      sha:
        type: string
        required: true
        description: "Commit SHA"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation"
      node_id:
        type: string
        description: "GitHub node ID"
      html_url:
        type: string
        description: "Commit web URL"
      message:
        type: string
        description: "Commit message"
      additions:
        type: integer
        description: "Number of additions"
      deletions:
        type: integer
        description: "Number of deletions"
      total:
        type: integer
        description: "Total changes"
      created_at:
        type: timestamp
        description: "Commit timestamp"

  GitHubCodeFile:
    description: "Represents a code file in GitHub"
    properties:
      path:
        type: string
        required: true
        description: "File path"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation"
      name:
        type: string
        description: "File name"
      sha:
        type: string
        description: "File SHA"
      size:
        type: integer
        description: "File size in bytes"
      html_url:
        type: string
        description: "File web URL"
      download_url:
        type: string
        description: "File download URL"
      type:
        type: string
        description: "File type"
      content:
        type: string
        description: "File content"
      encoding:
        type: string
        description: "Content encoding"
      vector_id:
        type: string
        description: "Pinecone vector ID for semantic search"
      vectorized_at:
        type: timestamp
        description: "When file was vectorized"
      content_hash:
        type: string
        description: "Content hash for change detection"
      last_processed:
        type: timestamp
        description: "Last processing timestamp"
      processing_status:
        type: string
        description: "Processing status"

relationships:
  HAS_ACCESS:
    from: User
    to: [GitHubRepository, GitHubCodeFile, GitHubIssue, GitHubPullRequest]
    description: "User has access to GitHub resource"
    direction: "->"
    properties:
      access_type:
        type: string
        description: "Type of access (owner, collaborator, viewer)"
      granted_at:
        type: timestamp
        description: "When access was granted"

  OWNS_REPOSITORY:
    from: GitHubUser
    to: GitHubRepository
    description: "User owns repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When ownership was established"

  CREATES_ISSUE:
    from: GitHubUser
    to: GitHubIssue
    description: "User creates issue"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When issue was created"

  CREATES_PULL_REQUEST:
    from: GitHubUser
    to: GitHubPullRequest
    description: "User creates pull request"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When pull request was created"

  AUTHORS_COMMIT:
    from: GitHubUser
    to: GitHubCommit
    description: "User authors commit"
    direction: "->"
    properties:
      authored_at:
        type: timestamp
        description: "When commit was authored"

  CONTAINS_FILE:
    from: GitHubRepository
    to: GitHubCodeFile
    description: "Repository contains file"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When file was added"

  BELONGS_TO_REPO:
    from: [GitHubIssue, GitHubPullRequest, GitHubCommit]
    to: GitHubRepository
    description: "Resource belongs to repository"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When relationship was created"

  MODIFIES_FILE:
    from: GitHubCommit
    to: GitHubCodeFile
    description: "Commit modifies file"
    direction: "->"
    properties:
      additions:
        type: integer
        description: "Number of additions"
      deletions:
        type: integer
        description: "Number of deletions"
      status:
        type: string
        description: "Change status (added/modified/removed)"
