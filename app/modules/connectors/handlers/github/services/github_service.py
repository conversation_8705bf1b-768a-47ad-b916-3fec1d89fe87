import os
import json
import time
from datetime import datetime, timedelta
import structlog
from typing import Tuple, List, Dict, Any, Optional
import requests
from requests.exceptions import RequestException

from app.core.config import settings
from app.services.neo4j_service import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.file_processing.generic_file_service import GenericFileService
from app.utils.source_credentials import get_source_credentials
from app.modules.connectors.handlers.github.repository.github_queries import (
    GitHubUserQueries,
    GitHubRepositoryQueries,
    GitHubIssueQueries,
    GitHubPullRequestQueries,
    GitHubCommitQueries,
    GitHubFileQueries,
    GitHubSyncQueries,
    GitHubRelationshipQueries,
    GitHubMetadataQueries
)
import uuid

logger = structlog.get_logger()

class GitHubService:
    """
    Service for GitHub integration operations.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()
        
        # Initialize repository instances
        self.user_queries = GitHubUserQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.issue_queries = GitHubIssueQueries()
        self.pull_request_queries = GitHubPullRequestQueries()
        self.commit_queries = GitHubCommitQueries()
        self.file_queries = GitHubFileQueries()
        self.sync_queries = GitHubSyncQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.metadata_queries = GitHubMetadataQueries()
        
    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.
        
        Args:
            github_url: The GitHub URL
            
        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456
            
            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}
            
            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None

    def sync_repository_by_url(self,
                              github_url: str,
                              agent_id: str,
                              user_id: Optional[str],
                              organisation_id: str,
                              full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync a specific GitHub repository by URL.
        
        Args:
            github_url: GitHub repository URL
            agent_id: Agent ID performing the sync
            user_id: User ID (optional)
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub repository sync for URL: {github_url}")
            
            # Extract repository info from URL
            repo_info = self.extract_repo_info_from_url(github_url)
            if not repo_info:
                return False, "Failed to extract repository information from URL", 0
            
            owner = repo_info["owner"]
            repo_name = repo_info["repo"]
            
            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0
            
            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0
            
            # Create session with authentication
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })
            
            # Sync repository data
            items_synced = 0
            
            # 1. Sync repository metadata
            repo_data = self._fetch_repository_data(session, owner, repo_name)
            if repo_data:
                self._create_or_update_repository(repo_data, organisation_id)
                items_synced += 1
                
                # 2. Sync issues if full sync
                if full_sync:
                    issues = self._fetch_repository_issues(session, owner, repo_name)
                    for issue in issues:
                        self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                        items_synced += 1
                
                # 3. Sync pull requests if full sync
                if full_sync:
                    pull_requests = self._fetch_repository_pull_requests(session, owner, repo_name)
                    for pr in pull_requests:
                        self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                        items_synced += 1
                
                # 4. Sync recent commits
                commits = self._fetch_repository_commits(session, owner, repo_name, limit=50)
                for commit in commits:
                    self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                    items_synced += 1
                
                # 5. Create organizational relationships
                self._create_organizational_relationships(repo_data['id'], organisation_id)
                
                logger.info(f"Successfully synced GitHub repository: {owner}/{repo_name}, items: {items_synced}")
                return True, f"Successfully synced repository {owner}/{repo_name}", items_synced
            else:
                return False, f"Failed to fetch repository data for {owner}/{repo_name}", 0
                
        except Exception as e:
            logger.error(f"Error syncing GitHub repository: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0

    def _fetch_repository_data(self, session: requests.Session, owner: str, repo: str) -> Optional[Dict]:
        """Fetch repository data from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}"
            response = session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository data: {str(e)}")
            return None

    def _fetch_repository_issues(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository issues from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/issues"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository issues: {str(e)}")
            return []

    def _fetch_repository_pull_requests(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository pull requests from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository pull requests: {str(e)}")
            return []

    def _fetch_repository_commits(self, session: requests.Session, owner: str, repo: str, limit: int = 50) -> List[Dict]:
        """Fetch repository commits from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository commits: {str(e)}")
            return []

    def _create_or_update_repository(self, repo_data: Dict, organisation_id: str):
        """Create or update repository in Neo4j."""
        try:
            params = {
                'id': repo_data['id'],
                'organisation_id': organisation_id,
                'node_id': repo_data.get('node_id'),
                'name': repo_data.get('name'),
                'full_name': repo_data.get('full_name'),
                'html_url': repo_data.get('html_url'),
                'description': repo_data.get('description'),
                'private': repo_data.get('private', False),
                'fork': repo_data.get('fork', False),
                'archived': repo_data.get('archived', False),
                'language': repo_data.get('language'),
                'stargazers_count': repo_data.get('stargazers_count', 0),
                'watchers_count': repo_data.get('watchers_count', 0),
                'forks_count': repo_data.get('forks_count', 0),
                'open_issues_count': repo_data.get('open_issues_count', 0),
                'default_branch': repo_data.get('default_branch'),
                'created_at': repo_data.get('created_at'),
                'updated_at': repo_data.get('updated_at'),
                'pushed_at': repo_data.get('pushed_at'),
                'license': repo_data.get('license', {}).get('spdx_id') if repo_data.get('license') else None
            }
            
            execute_write_query(self.repository_queries.CREATE_REPOSITORY, params)
            
            # Create owner relationship if owner data exists
            if repo_data.get('owner'):
                self._create_or_update_user(repo_data['owner'], organisation_id)
                self._create_repository_owner_relationship(
                    repo_data['owner']['id'], 
                    repo_data['id'], 
                    organisation_id
                )
                
        except Exception as e:
            logger.error(f"Error creating/updating repository: {str(e)}")

    def _create_or_update_user(self, user_data: Dict, organisation_id: str):
        """Create or update GitHub user in Neo4j."""
        try:
            params = {
                'id': user_data['id'],
                'organisation_id': organisation_id,
                'node_id': user_data.get('node_id'),
                'login': user_data.get('login'),
                'name': user_data.get('name'),
                'email': user_data.get('email'),
                'html_url': user_data.get('html_url'),
                'avatar_url': user_data.get('avatar_url'),
                'type': user_data.get('type'),
                'site_admin': user_data.get('site_admin', False),
                'created_at': user_data.get('created_at'),
                'updated_at': user_data.get('updated_at')
            }
            
            execute_write_query(self.user_queries.CREATE_GITHUB_USER, params)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub user: {str(e)}")

    def _create_or_update_issue(self, issue_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub issue in Neo4j."""
        try:
            params = {
                'id': issue_data['id'],
                'organisation_id': organisation_id,
                'node_id': issue_data.get('node_id'),
                'number': issue_data.get('number'),
                'html_url': issue_data.get('html_url'),
                'title': issue_data.get('title'),
                'body': issue_data.get('body'),
                'state': issue_data.get('state'),
                'labels': json.dumps([label.get('name') for label in issue_data.get('labels', [])]),
                'comments': issue_data.get('comments', 0),
                'created_at': issue_data.get('created_at'),
                'updated_at': issue_data.get('updated_at'),
                'closed_at': issue_data.get('closed_at')
            }
            
            execute_write_query(self.issue_queries.CREATE_ISSUE, params)
            
            # Create relationships
            if issue_data.get('user'):
                self._create_or_update_user(issue_data['user'], organisation_id)
                self._create_issue_creator_relationship(
                    issue_data['user']['id'],
                    issue_data['id'],
                    organisation_id
                )
            
            self._create_issue_repository_relationship(issue_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub issue: {str(e)}")

    def _create_or_update_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub pull request in Neo4j."""
        try:
            params = {
                'id': pr_data['id'],
                'organisation_id': organisation_id,
                'node_id': pr_data.get('node_id'),
                'number': pr_data.get('number'),
                'html_url': pr_data.get('html_url'),
                'title': pr_data.get('title'),
                'body': pr_data.get('body'),
                'state': pr_data.get('state'),
                'draft': pr_data.get('draft', False),
                'merged': pr_data.get('merged', False),
                'mergeable': pr_data.get('mergeable'),
                'additions': pr_data.get('additions', 0),
                'deletions': pr_data.get('deletions', 0),
                'changed_files': pr_data.get('changed_files', 0),
                'created_at': pr_data.get('created_at'),
                'updated_at': pr_data.get('updated_at'),
                'closed_at': pr_data.get('closed_at'),
                'merged_at': pr_data.get('merged_at')
            }
            
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST, params)
            
            # Create relationships
            if pr_data.get('user'):
                self._create_or_update_user(pr_data['user'], organisation_id)
                self._create_pull_request_creator_relationship(
                    pr_data['user']['id'],
                    pr_data['id'],
                    organisation_id
                )
            
            self._create_pull_request_repository_relationship(pr_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub pull request: {str(e)}")

    def _create_or_update_commit(self, commit_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub commit in Neo4j."""
        try:
            params = {
                'sha': commit_data['sha'],
                'organisation_id': organisation_id,
                'node_id': commit_data.get('node_id'),
                'html_url': commit_data.get('html_url'),
                'message': commit_data.get('commit', {}).get('message'),
                'additions': commit_data.get('stats', {}).get('additions', 0),
                'deletions': commit_data.get('stats', {}).get('deletions', 0),
                'total': commit_data.get('stats', {}).get('total', 0),
                'created_at': commit_data.get('commit', {}).get('author', {}).get('date')
            }
            
            execute_write_query(self.commit_queries.CREATE_COMMIT, params)
            
            # Create relationships
            if commit_data.get('author'):
                self._create_or_update_user(commit_data['author'], organisation_id)
                self._create_commit_author_relationship(
                    commit_data['author']['id'],
                    commit_data['sha'],
                    organisation_id
                )
            
            self._create_commit_repository_relationship(commit_data['sha'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub commit: {str(e)}")

    def _create_repository_owner_relationship(self, user_id: int, repository_id: int, organisation_id: str):
        """Create repository owner relationship."""
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.repository_queries.CREATE_REPOSITORY_OWNER_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating repository owner relationship: {str(e)}")

    def _create_issue_creator_relationship(self, user_id: int, issue_id: int, organisation_id: str):
        """Create issue creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'issue_id': issue_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue creator relationship: {str(e)}")

    def _create_issue_repository_relationship(self, issue_id: int, repository_id: int, organisation_id: str):
        """Create issue repository relationship."""
        try:
            params = {
                'issue_id': issue_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue repository relationship: {str(e)}")

    def _create_pull_request_creator_relationship(self, user_id: int, pull_request_id: int, organisation_id: str):
        """Create pull request creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'pull_request_id': pull_request_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request creator relationship: {str(e)}")

    def _create_pull_request_repository_relationship(self, pull_request_id: int, repository_id: int, organisation_id: str):
        """Create pull request repository relationship."""
        try:
            params = {
                'pull_request_id': pull_request_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request repository relationship: {str(e)}")

    def _create_commit_author_relationship(self, user_id: int, commit_sha: str, organisation_id: str):
        """Create commit author relationship."""
        try:
            params = {
                'user_id': user_id,
                'commit_sha': commit_sha,
                'organisation_id': organisation_id,
                'authored_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_AUTHOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit author relationship: {str(e)}")

    def _create_commit_repository_relationship(self, commit_sha: str, repository_id: int, organisation_id: str):
        """Create commit repository relationship."""
        try:
            params = {
                'commit_sha': commit_sha,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit repository relationship: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create relationships between GitHub repository and organizational entities."""
        try:
            # Create organisation-repository relationship
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)
            
            # Create department-repository relationships for all departments
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)
            
        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")

    def sync_github(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Perform a full sync of GitHub data for an organisation.
        
        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, repositories_synced, total_items_synced)
        """
        try:
            logger.info(f"Starting GitHub sync for organisation: {organisation_id}")
            
            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0, 0
            
            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0, 0
            
            # Create session with authentication
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })
            
            repositories_synced = 0
            total_items_synced = 0
            
            # Fetch user's repositories
            repositories = self._fetch_user_repositories(session)
            
            for repo_data in repositories:
                try:
                    # Sync repository
                    self._create_or_update_repository(repo_data, organisation_id)
                    repositories_synced += 1
                    total_items_synced += 1
                    
                    if full_sync:
                        # Sync issues
                        issues = self._fetch_repository_issues(session, repo_data['owner']['login'], repo_data['name'])
                        for issue in issues:
                            self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                            total_items_synced += 1
                        
                        # Sync pull requests
                        pull_requests = self._fetch_repository_pull_requests(session, repo_data['owner']['login'], repo_data['name'])
                        for pr in pull_requests:
                            self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                            total_items_synced += 1
                    
                    # Sync recent commits
                    commits = self._fetch_repository_commits(session, repo_data['owner']['login'], repo_data['name'], limit=20)
                    for commit in commits:
                        self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                        total_items_synced += 1
                    
                    # Create organizational relationships
                    self._create_organizational_relationships(repo_data['id'], organisation_id)
                    
                except Exception as e:
                    logger.error(f"Error syncing repository {repo_data.get('full_name', 'unknown')}: {str(e)}")
                    continue
            
            # Update last sync time
            self._update_last_sync_time(organisation_id)
            
            logger.info(f"GitHub sync completed: {repositories_synced} repositories, {total_items_synced} total items")
            return True, f"Successfully synced {repositories_synced} repositories", repositories_synced, total_items_synced
            
        except Exception as e:
            logger.error(f"Error during GitHub sync: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0, 0

    def _fetch_user_repositories(self, session: requests.Session) -> List[Dict]:
        """Fetch user's repositories from GitHub API."""
        try:
            url = "https://api.github.com/user/repos"
            params = {'type': 'all', 'sort': 'updated', 'per_page': 100}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch user repositories: {str(e)}")
            return []

    def _update_last_sync_time(self, organisation_id: str):
        """Update the last sync time for the organisation."""
        try:
            params = {
                'organisation_id': organisation_id,
                'sync_time': datetime.now().isoformat()
            }
            execute_write_query(self.sync_queries.UPDATE_LAST_SYNC_TIME, params)
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def get_sync_statistics(self, organisation_id: str) -> Dict[str, Any]:
        """Get sync statistics for an organisation."""
        try:
            params = {'organisation_id': organisation_id}
            result = execute_read_query(self.sync_queries.GET_SYNC_STATISTICS, params)
            
            if result:
                return result[0]
            return {}
            
        except Exception as e:
            logger.error(f"Error getting sync statistics: {str(e)}")
            return {}

    def search_github_content(self, organisation_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search GitHub content using hybrid search.
        
        Args:
            organisation_id: Organisation ID
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            # Use hybrid search engine for GitHub content
            search_engine = HybridSearchEngine()
            
            # Define GitHub entity types for search
            entity_types = [
                'GitHubRepository',
                'GitHubIssue', 
                'GitHubPullRequest',
                'GitHubCodeFile'
            ]
            
            results = search_engine.search(
                query=query,
                organisation_id=organisation_id,
                entity_types=entity_types,
                limit=limit
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching GitHub content: {str(e)}")
            return []

    def get_repository_statistics(self, organisation_id: str, repository_id: int) -> Dict[str, Any]:
        """Get statistics for a specific repository."""
        try:
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id
            }
            result = execute_read_query(self.metadata_queries.GET_REPOSITORY_STATISTICS, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting repository statistics: {str(e)}")
            return {}

    def get_user_activity_summary(self, organisation_id: str, user_id: int) -> Dict[str, Any]:
        """Get activity summary for a GitHub user."""
        try:
            params = {
                'organisation_id': organisation_id,
                'user_id': user_id
            }
            result = execute_read_query(self.metadata_queries.GET_USER_ACTIVITY_SUMMARY, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting user activity summary: {str(e)}")
            return {}
