"""
GitHub connector service implementation.
Inherits from BaseConnector and implements GitHub-specific functionality.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import json
import hashlib

from app.modules.connectors.base import BaseConnector
from app.modules.connectors.utilities.constant.schemas import (
    ConnectorSearchResponse, SearchResultItem, SyncResult
)
from app.modules.connectors.handlers.github.connection import GitHubConnection
from app.modules.connectors.handlers.github.schema import (
    GitHubConnectorConfig, GitHubSyncResult, GitHubSearchRequest,
    GitHubRepository, GitHubIssue, GitHubPullRequest, GitHubCommit,
    GitHubRelease, GitHubWorkflow, GitHubWorkflowRun, GitHubContent
)
from app.modules.connectors.handlers.github.constants.entities import (
    GitHubEntityType, GitHubEntityProperties
)
from app.modules.connectors.handlers.github.constants.relationships import (
    GitHubRelationshipType
)

logger = logging.getLogger(__name__)

class GitHubConnector(BaseConnector):
    """GitHub connector implementation"""
    
    def __init__(self, config: Dict[str, Any], organization_id: str, user_id: str):
        super().__init__(config, organization_id, user_id)
        self.config = config
        self.connection: Optional[GitHubConnection] = None
        self.connector_type = "github"
        self.last_sync_time: Optional[datetime] = None
        self._connected = False
        
    def connect(self) -> bool:
        """Establish connection to GitHub API"""
        try:
            logger.info("Connecting to GitHub API...")
            
            self.connection = GitHubConnection(self.config)
            
            if self.connection.connect():
                self._connected = True
                logger.info("Successfully connected to GitHub API")
                return True
            else:
                raise ConnectionError("Failed to establish GitHub API connection")
                
        except Exception as e:
            logger.error(f"GitHub connection failed: {str(e)}")
            self._connected = False
            raise
    
    def disconnect(self):
        """Close GitHub connection"""
        if self.connection:
            self.connection.disconnect()
            self.connection = None
        self._connected = False
    
    def health_check(self) -> Dict[str, Any]:
        """Check connector health"""
        if not self.connection:
            return {
                'status': 'disconnected',
                'message': 'No active connection'
            }
        
        return self.connection.get_connection_info()
    
    def fetch_data(self, data_type: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Fetch data from GitHub based on type and filters"""
        if not self.connection:
            raise RuntimeError("Not connected to GitHub. Call connect() first.")
        
        filters = filters or {}
        
        try:
            if data_type == "repositories":
                return self._fetch_repositories(filters)
            elif data_type == "issues":
                return self._fetch_issues(filters)
            elif data_type == "pull_requests":
                return self._fetch_pull_requests(filters)
            elif data_type == "commits":
                return self._fetch_commits(filters)
            elif data_type == "releases":
                return self._fetch_releases(filters)
            elif data_type == "workflows":
                return self._fetch_workflows(filters)
            elif data_type == "files":
                return self._fetch_files(filters)
            else:
                raise ValueError(f"Unsupported data type: {data_type}")
                
        except ConnectionError as e:
            logger.error(f"GitHub API error fetching {data_type}: {str(e)}")
            raise
    
    def _fetch_repositories(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch repositories from GitHub"""
        repositories = []
        
        organization = self.config.get('organization')
        if organization:
            # Fetch organization repositories
            repos = self.connection.get_organization_repositories(organization)
        else:
            # Fetch user repositories
            repos = self.connection.get_user_repositories()
        
        max_repositories = self.config.get('max_repositories', 100)
        include_forks = self.config.get('include_forks', True)
        include_archived = self.config.get('include_archived', True)
        repositories_filter = self.config.get('repositories', [])
        
        for repo_data in repos:
            # Apply filters
            if not include_forks and repo_data.get('fork', False):
                continue
            if not include_archived and repo_data.get('archived', False):
                continue
            
            # Limit repositories if specified
            if repositories_filter and repo_data['full_name'] not in repositories_filter:
                continue
            
            # Convert to our format
            repository = self._process_repository(repo_data)
            repositories.append(repository)
            
            if len(repositories) >= max_repositories:
                break
        
        return repositories
    
    def _fetch_issues(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch issues from GitHub repositories"""
        sync_issues = self.config.get('sync_issues', True)
        if not sync_issues:
            return []
        
        issues = []
        repo_name = filters.get('repository')
        
        if repo_name:
            max_issues_per_repo = self.config.get('max_issues_per_repo', 1000)
            # Fetch issues for specific repository
            repo_issues = self.connection.get_paginated(
                f'/repos/{repo_name}/issues',
                {'state': 'all', 'sort': 'updated', 'direction': 'desc'},
                max_pages=max_issues_per_repo // 100
            )
            
            for issue_data in repo_issues:
                # Skip pull requests (they appear in issues API)
                if 'pull_request' in issue_data:
                    continue
                
                issue = self._process_issue(issue_data, repo_name)
                issues.append(issue)
        
        return issues
    
    def _fetch_pull_requests(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch pull requests from GitHub repositories"""
        sync_pull_requests = self.config.get('sync_pull_requests', True)
        if not sync_pull_requests:
            return []
        
        pull_requests = []
        repo_name = filters.get('repository')
        
        if repo_name:
            max_prs_per_repo = self.config.get('max_prs_per_repo', 1000)
            # Fetch PRs for specific repository
            prs = self.connection.get_paginated(
                f'/repos/{repo_name}/pulls',
                {'state': 'all', 'sort': 'updated', 'direction': 'desc'},
                max_pages=max_prs_per_repo // 100
            )
            
            for pr_data in prs:
                pr = self._process_pull_request(pr_data, repo_name)
                pull_requests.append(pr)
        
        return pull_requests
    
    async def _fetch_commits(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch commits from GitHub repositories"""
        if not self.github_config.sync_commits:
            return []
        
        commits = []
        repo_name = filters.get('repository')
        branch = filters.get('branch', 'main')
        
        if repo_name:
            # Fetch commits for specific repository and branch
            commit_data = await self.connection.get_paginated(
                f'/repos/{repo_name}/commits',
                {'sha': branch, 'per_page': 100},
                max_pages=self.github_config.max_commits_per_repo // 100
            )
            
            for commit_info in commit_data:
                commit = await self._process_commit(commit_info, repo_name)
                commits.append(commit)
        
        return commits
    
    async def _fetch_releases(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch releases from GitHub repositories"""
        if not self.github_config.sync_releases:
            return []
        
        releases = []
        repo_name = filters.get('repository')
        
        if repo_name:
            # Fetch releases for specific repository
            release_data = await self.connection.get_paginated(
                f'/repos/{repo_name}/releases',
                {'per_page': 100}
            )
            
            for release_info in release_data:
                release = await self._process_release(release_info, repo_name)
                releases.append(release)
        
        return releases
    
    async def _fetch_workflows(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch GitHub Actions workflows"""
        if not self.github_config.sync_workflows:
            return []
        
        workflows = []
        repo_name = filters.get('repository')
        
        if repo_name:
            # Fetch workflows for specific repository
            workflow_data = await self.connection.get_paginated(
                f'/repos/{repo_name}/actions/workflows',
                {'per_page': 100}
            )
            
            for workflow_info in workflow_data.get('workflows', []):
                workflow = await self._process_workflow(workflow_info, repo_name)
                workflows.append(workflow)
        
        return workflows
    
    async def _fetch_files(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch files from GitHub repositories"""
        files = []
        repo_name = filters.get('repository')
        path = filters.get('path', '')
        ref = filters.get('ref', 'main')
        
        if repo_name:
            try:
                # Get repository contents
                contents = await self.connection.get_paginated(
                    f'/repos/{repo_name}/contents/{path}',
                    {'ref': ref}
                )
                
                for content_item in contents:
                    if content_item.get('type') == 'file':
                        file_data = await self._process_file(content_item, repo_name)
                        files.append(file_data)
                    elif content_item.get('type') == 'dir':
                        # Recursively fetch directory contents
                        subdir_files = await self._fetch_files({
                            'repository': repo_name,
                            'path': content_item['path'],
                            'ref': ref
                        })
                        files.extend(subdir_files)
                        
            except GitHubAPIError as e:
                if e.status_code != 404:  # Ignore not found errors
                    raise
        
        return files
    
    def _process_repository(self, repo_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process repository data into standardized format"""
        return {
            'id': f"github_repo_{repo_data['id']}",
            'entity_type': 'GitHubRepository',
            'name': repo_data['name'],
            'full_name': repo_data['full_name'],
            'description': repo_data.get('description', ''),
            'url': repo_data['html_url'],
            'clone_url': repo_data['clone_url'],
            'language': repo_data.get('language'),
            'topics': repo_data.get('topics', []),
            'stars_count': repo_data['stargazers_count'],
            'forks_count': repo_data['forks_count'],
            'open_issues_count': repo_data['open_issues_count'],
            'private': repo_data['private'],
            'fork': repo_data['fork'],
            'archived': repo_data['archived'],
            'created_at': repo_data['created_at'],
            'updated_at': repo_data['updated_at'],
            'pushed_at': repo_data.get('pushed_at'),
            'default_branch': repo_data['default_branch'],
            'license': repo_data.get('license', {}).get('name') if repo_data.get('license') else None,
            'owner': {
                'login': repo_data['owner']['login'],
                'type': repo_data['owner']['type'],
                'url': repo_data['owner']['html_url']
            },
            'content': f"{repo_data['name']} - {repo_data.get('description', '')}",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': str(repo_data['id']),
                'last_synced': datetime.now().isoformat()
            }
        }
    
    def _process_issue(self, issue_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process issue data into standardized format"""
        return {
            'id': f"github_issue_{issue_data['id']}",
            'entity_type': 'GitHubIssue',
            'number': issue_data['number'],
            'title': issue_data['title'],
            'body': issue_data.get('body', ''),
            'state': issue_data['state'],
            'url': issue_data['html_url'],
            'repository': repo_name,
            'author': {
                'login': issue_data['user']['login'],
                'url': issue_data['user']['html_url']
            },
            'assignees': [assignee['login'] for assignee in issue_data.get('assignees', [])],
            'labels': [label['name'] for label in issue_data.get('labels', [])],
            'comments_count': issue_data['comments'],
            'created_at': issue_data['created_at'],
            'updated_at': issue_data['updated_at'],
            'closed_at': issue_data.get('closed_at'),
            'content': f"{issue_data['title']} - {issue_data.get('body', '')}",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': str(issue_data['id']),
                'repository': repo_name,
                'last_synced': datetime.now().isoformat()
            }
        }
    
    def _process_pull_request(self, pr_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process pull request data into standardized format"""
        return {
            'id': f"github_pr_{pr_data['id']}",
            'entity_type': 'GitHubPullRequest',
            'number': pr_data['number'],
            'title': pr_data['title'],
            'body': pr_data.get('body', ''),
            'state': pr_data['state'],
            'url': pr_data['html_url'],
            'repository': repo_name,
            'author': {
                'login': pr_data['user']['login'],
                'url': pr_data['user']['html_url']
            },
            'head_branch': pr_data['head']['ref'],
            'base_branch': pr_data['base']['ref'],
            'merged': pr_data.get('merged', False),
            'draft': pr_data.get('draft', False),
            'assignees': [assignee['login'] for assignee in pr_data.get('assignees', [])],
            'labels': [label['name'] for label in pr_data.get('labels', [])],
            'comments_count': pr_data['comments'],
            'review_comments_count': pr_data['review_comments'],
            'commits_count': pr_data['commits'],
            'additions': pr_data.get('additions', 0),
            'deletions': pr_data.get('deletions', 0),
            'changed_files': pr_data.get('changed_files', 0),
            'created_at': pr_data['created_at'],
            'updated_at': pr_data['updated_at'],
            'closed_at': pr_data.get('closed_at'),
            'merged_at': pr_data.get('merged_at'),
            'content': f"{pr_data['title']} - {pr_data.get('body', '')}",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': str(pr_data['id']),
                'repository': repo_name,
                'last_synced': datetime.now().isoformat()
            }
        }
    
    async def _process_commit(self, commit_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process commit data into standardized format"""
        commit_info = commit_data.get('commit', {})
        author_info = commit_info.get('author', {})
        
        return {
            'id': f"github_commit_{commit_data['sha']}",
            'entity_type': GitHubEntityType.COMMIT.value,
            'sha': commit_data['sha'],
            'message': commit_info.get('message', ''),
            'url': commit_data['html_url'],
            'repository': repo_name,
            'author': {
                'name': author_info.get('name'),
                'email': author_info.get('email'),
                'date': author_info.get('date')
            },
            'committer': {
                'name': commit_info.get('committer', {}).get('name'),
                'email': commit_info.get('committer', {}).get('email'),
                'date': commit_info.get('committer', {}).get('date')
            },
            'stats': commit_data.get('stats', {}),
            'files_changed': len(commit_data.get('files', [])),
            'content': commit_info.get('message', ''),
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': commit_data['sha'],
                'repository': repo_name,
                'last_synced': datetime.now().isoformat()
            }
        }
    
    async def _process_release(self, release_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process release data into standardized format"""
        return {
            'id': f"github_release_{release_data['id']}",
            'entity_type': GitHubEntityType.RELEASE.value,
            'tag_name': release_data['tag_name'],
            'name': release_data.get('name', release_data['tag_name']),
            'body': release_data.get('body', ''),
            'url': release_data['html_url'],
            'repository': repo_name,
            'author': {
                'login': release_data['author']['login'],
                'url': release_data['author']['html_url']
            },
            'draft': release_data['draft'],
            'prerelease': release_data['prerelease'],
            'created_at': release_data['created_at'],
            'published_at': release_data.get('published_at'),
            'assets_count': len(release_data.get('assets', [])),
            'content': f"{release_data.get('name', release_data['tag_name'])} - {release_data.get('body', '')}",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': str(release_data['id']),
                'repository': repo_name,
                'last_synced': datetime.now().isoformat()
            }
        }
    
    async def _process_workflow(self, workflow_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process workflow data into standardized format"""
        return {
            'id': f"github_workflow_{workflow_data['id']}",
            'entity_type': GitHubEntityType.WORKFLOW.value,
            'name': workflow_data['name'],
            'path': workflow_data['path'],
            'state': workflow_data['state'],
            'url': workflow_data['html_url'],
            'repository': repo_name,
            'created_at': workflow_data['created_at'],
            'updated_at': workflow_data['updated_at'],
            'content': f"Workflow: {workflow_data['name']} ({workflow_data['path']})",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': str(workflow_data['id']),
                'repository': repo_name,
                'last_synced': datetime.now().isoformat()
            }
        }
    
    async def _process_file(self, file_data: Dict[str, Any], repo_name: str) -> Dict[str, Any]:
        """Process file data into standardized format"""
        # Get file content if it's a text file and not too large
        content = ""
        if file_data.get('size', 0) < 1024 * 1024:  # 1MB limit
            try:
                content = await self.connection.get_file_content(
                    repo_name, file_data['path']
                )
            except:
                content = ""  # Ignore errors getting content
        
        return {
            'id': f"github_file_{hashlib.md5(f'{repo_name}_{file_data['path']}'.encode()).hexdigest()}",
            'entity_type': GitHubEntityType.CODE_FILE.value,
            'name': file_data['name'],
            'path': file_data['path'],
            'size': file_data.get('size', 0),
            'url': file_data.get('html_url', ''),
            'download_url': file_data.get('download_url', ''),
            'repository': repo_name,
            'content': content or f"File: {file_data['name']} at {file_data['path']}",
            'metadata': {
                'connector_type': self.connector_type,
                'source_id': file_data['sha'],
                'repository': repo_name,
                'file_type': file_data.get('type', 'file'),
                'last_synced': datetime.now().isoformat()
            }
        }
    
    async def sync(self, incremental: bool = True) -> SyncResult:
        """Sync data from GitHub"""
        if not self.connection:
            raise RuntimeError("Not connected to GitHub. Call connect() first.")
        
        sync_start = datetime.now()
        sync_result = GitHubSyncResult()
        
        try:
            # Fetch repositories first
            repositories = await self.fetch_data("repositories")
            sync_result.repositories_synced = len(repositories)
            
            # Store repositories
            for repo in repositories:
                await self.store_context(repo)
            
            # For each repository, fetch additional data
            for repo in repositories:
                repo_name = repo['full_name']
                
                try:
                    # Fetch issues
                    if self.github_config.sync_issues:
                        issues = await self.fetch_data("issues", {"repository": repo_name})
                        sync_result.issues_synced += len(issues)
                        for issue in issues:
                            await self.store_context(issue)
                    
                    # Fetch pull requests
                    if self.github_config.sync_pull_requests:
                        prs = await self.fetch_data("pull_requests", {"repository": repo_name})
                        sync_result.pull_requests_synced += len(prs)
                        for pr in prs:
                            await self.store_context(pr)
                    
                    # Fetch commits
                    if self.github_config.sync_commits:
                        commits = await self.fetch_data("commits", {"repository": repo_name})
                        sync_result.commits_synced += len(commits)
                        for commit in commits:
                            await self.store_context(commit)
                    
                    # Fetch releases
                    if self.github_config.sync_releases:
                        releases = await self.fetch_data("releases", {"repository": repo_name})
                        sync_result.releases_synced += len(releases)
                        for release in releases:
                            await self.store_context(release)
                    
                    # Small delay between repositories
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    error_msg = f"Error syncing repository {repo_name}: {str(e)}"
                    logger.error(error_msg)
                    sync_result.errors.append(error_msg)
            
            self.last_sync_time = datetime.now()
            sync_result.last_sync_timestamp = self.last_sync_time
            sync_result.sync_duration = (datetime.now() - sync_start).total_seconds()
            
            logger.info(f"GitHub sync completed: {sync_result.repositories_synced} repos, "
                       f"{sync_result.issues_synced} issues, {sync_result.pull_requests_synced} PRs, "
                       f"{sync_result.commits_synced} commits")
            
            return SyncResult(
                success=True,
                items_processed=sync_result.repositories_synced + sync_result.issues_synced + 
                               sync_result.pull_requests_synced + sync_result.commits_synced,
                items_added=sync_result.repositories_synced + sync_result.issues_synced + 
                           sync_result.pull_requests_synced + sync_result.commits_synced,
                items_updated=0,
                items_failed=len(sync_result.errors),
                errors=sync_result.errors,
                sync_duration=sync_result.sync_duration,
                last_sync_time=self.last_sync_time
            )
            
        except Exception as e:
            logger.error(f"GitHub sync failed: {str(e)}")
            return SyncResult(
                success=False,
                items_processed=0,
                items_added=0,
                items_updated=0,
                items_failed=1,
                errors=[str(e)],
                sync_duration=(datetime.now() - sync_start).total_seconds()
            )
    
    async def search(self, query: str, search_type: str = "repositories", 
                    filters: Optional[Dict[str, Any]] = None) -> ConnectorSearchResponse:
        """Search GitHub data"""
        if not self.connection:
            raise RuntimeError("Not connected to GitHub. Call connect() first.")
        
        try:
            # Use GitHub's search API
            search_results = await self.connection.search(
                search_type=search_type,
                query=query,
                per_page=30
            )
            
            items = []
            for item in search_results.get('items', []):
                search_item = SearchResultItem(
                    id=f"github_{search_type}_{item['id']}",
                    title=item.get('name') or item.get('title', ''),
                    content=item.get('description') or item.get('body', ''),
                    url=item.get('html_url', ''),
                    source=self.connector_type,
                    metadata={
                        'type': search_type,
                        'score': item.get('score', 0),
                        'repository': item.get('full_name') or item.get('repository', {}).get('full_name'),
                        'created_at': item.get('created_at'),
                        'updated_at': item.get('updated_at')
                    }
                )
                items.append(search_item)
            
            return ConnectorSearchResponse(
                query=query,
                total_results=search_results.get('total_count', 0),
                items=items,
                source=self.connector_type,
                search_time=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"GitHub search failed: {str(e)}")
            return ConnectorSearchResponse(
                query=query,
                total_results=0,
                items=[],
                source=self.connector_type,
                search_time=datetime.now(),
                error=str(e)
            )
    
    def get_supported_data_types(self) -> List[str]:
        """Get list of supported data types"""
        return [
            "repositories",
            "issues", 
            "pull_requests",
            "commits",
            "releases",
            "workflows",
            "files"
        ]
    
    def get_connector_info(self) -> Dict[str, Any]:
        """Get connector information"""
        return {
            "name": "GitHub",
            "type": self.connector_type,
            "version": "1.0.0",
            "description": "GitHub connector for repositories, issues, pull requests, and code",
            "supported_data_types": self.get_supported_data_types(),
            "authentication": "Personal Access Token",
            "rate_limits": {
                "authenticated": 5000,
                "current_remaining": getattr(self.connection, 'rate_limit_remaining', 0) if self.connection else 0
            },
            "last_sync": self.last_sync_time.isoformat() if self.last_sync_time else None,
            "is_connected": self.is_connected
        }