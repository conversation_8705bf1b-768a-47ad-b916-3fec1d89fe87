"""
GitHub Connector Neo4j Queries

This module contains all Neo4j queries for the GitHub connector,
organized by entity type and functionality.
"""

import structlog

logger = structlog.get_logger()


class GitHubBaseQueries:
    """Base class for GitHub queries with common labels and relationships."""
    
    def __init__(self):
        # Core organizational labels
        self.organisation_label = "Organisation"
        self.department_label = "Department"
        self.user_label = "User"
        self.agent_label = "Agent"
        
        # GitHub entity labels
        self.repository_label = "GitHubRepository"
        self.user_github_label = "GitHubUser"
        self.issue_label = "GitHubIssue"
        self.pull_request_label = "GitHubPullRequest"
        self.commit_label = "GitHubCommit"
        self.file_label = "GitHubCodeFile"
        self.organization_label = "GitHubOrganization"
        self.branch_label = "GitHubBranch"
        self.tag_label = "GitHubTag"
        self.release_label = "GitHubRelease"
        
        # Common relationships
        self.has_access_rel = "HAS_ACCESS"
        self.belongs_to_rel = "BELONGS_TO"
        self.owns_rel = "OWNS"
        self.creates_rel = "CREATES"
        self.contains_rel = "CONTAINS"
        self.modifies_rel = "MODIFIES"


class GitHubUserQueries(GitHubBaseQueries):
    """Queries for GitHub user operations."""
    
    @property
    def CREATE_GITHUB_USER(self):
        return f"""
        MERGE (u:{self.user_github_label} {{id: $id}})
        SET u.organisation_id = $organisation_id,
            u.node_id = $node_id,
            u.login = $login,
            u.name = $name,
            u.email = $email,
            u.html_url = $html_url,
            u.avatar_url = $avatar_url,
            u.type = $type,
            u.site_admin = $site_admin,
            u.created_at = $created_at,
            u.updated_at = $updated_at
        RETURN u
        """
    
    @property
    def GET_GITHUB_USER_BY_ID(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $id, organisation_id: $organisation_id}})
        RETURN u
        """
    
    @property
    def GET_GITHUB_USER_BY_LOGIN(self):
        return f"""
        MATCH (u:{self.user_github_label} {{login: $login, organisation_id: $organisation_id}})
        RETURN u
        """
    
    @property
    def LIST_GITHUB_USERS(self):
        return f"""
        MATCH (u:{self.user_github_label} {{organisation_id: $organisation_id}})
        RETURN u
        ORDER BY u.login
        LIMIT $limit
        """


class GitHubRepositoryQueries(GitHubBaseQueries):
    """Queries for GitHub repository operations."""
    
    @property
    def CREATE_REPOSITORY(self):
        return f"""
        MERGE (r:{self.repository_label} {{id: $id}})
        SET r.organisation_id = $organisation_id,
            r.node_id = $node_id,
            r.name = $name,
            r.full_name = $full_name,
            r.html_url = $html_url,
            r.description = $description,
            r.private = $private,
            r.fork = $fork,
            r.archived = $archived,
            r.language = $language,
            r.stargazers_count = $stargazers_count,
            r.watchers_count = $watchers_count,
            r.forks_count = $forks_count,
            r.open_issues_count = $open_issues_count,
            r.default_branch = $default_branch,
            r.created_at = $created_at,
            r.updated_at = $updated_at,
            r.pushed_at = $pushed_at,
            r.license = $license
        RETURN r
        """
    
    @property
    def GET_REPOSITORY_BY_ID(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $id, organisation_id: $organisation_id}})
        RETURN r
        """
    
    @property
    def GET_REPOSITORY_BY_FULL_NAME(self):
        return f"""
        MATCH (r:{self.repository_label} {{full_name: $full_name, organisation_id: $organisation_id}})
        RETURN r
        """
    
    @property
    def LIST_REPOSITORIES(self):
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        RETURN r
        ORDER BY r.updated_at DESC
        LIMIT $limit
        """
    
    @property
    def CREATE_REPOSITORY_OWNER_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (u)-[rel:OWNS_REPOSITORY]->(r)
        SET rel.created_at = $created_at
        RETURN rel
        """


class GitHubIssueQueries(GitHubBaseQueries):
    """Queries for GitHub issue operations."""
    
    @property
    def CREATE_ISSUE(self):
        return f"""
        MERGE (i:{self.issue_label} {{id: $id}})
        SET i.organisation_id = $organisation_id,
            i.node_id = $node_id,
            i.number = $number,
            i.html_url = $html_url,
            i.title = $title,
            i.body = $body,
            i.state = $state,
            i.labels = $labels,
            i.comments = $comments,
            i.created_at = $created_at,
            i.updated_at = $updated_at,
            i.closed_at = $closed_at
        RETURN i
        """
    
    @property
    def GET_ISSUE_BY_ID(self):
        return f"""
        MATCH (i:{self.issue_label} {{id: $id, organisation_id: $organisation_id}})
        RETURN i
        """
    
    @property
    def LIST_ISSUES_BY_REPOSITORY(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MATCH (i:{self.issue_label})-[:BELONGS_TO_REPO]->(r)
        RETURN i
        ORDER BY i.created_at DESC
        LIMIT $limit
        """
    
    @property
    def CREATE_ISSUE_CREATOR_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (i:{self.issue_label} {{id: $issue_id, organisation_id: $organisation_id}})
        MERGE (u)-[rel:CREATES_ISSUE]->(i)
        SET rel.created_at = $created_at
        RETURN rel
        """
    
    @property
    def CREATE_ISSUE_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (i:{self.issue_label} {{id: $issue_id, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (i)-[rel:BELONGS_TO_REPO]->(r)
        SET rel.created_at = $created_at
        RETURN rel
        """


class GitHubPullRequestQueries(GitHubBaseQueries):
    """Queries for GitHub pull request operations."""
    
    @property
    def CREATE_PULL_REQUEST(self):
        return f"""
        MERGE (pr:{self.pull_request_label} {{id: $id}})
        SET pr.organisation_id = $organisation_id,
            pr.node_id = $node_id,
            pr.number = $number,
            pr.html_url = $html_url,
            pr.title = $title,
            pr.body = $body,
            pr.state = $state,
            pr.draft = $draft,
            pr.merged = $merged,
            pr.mergeable = $mergeable,
            pr.additions = $additions,
            pr.deletions = $deletions,
            pr.changed_files = $changed_files,
            pr.created_at = $created_at,
            pr.updated_at = $updated_at,
            pr.closed_at = $closed_at,
            pr.merged_at = $merged_at
        RETURN pr
        """
    
    @property
    def GET_PULL_REQUEST_BY_ID(self):
        return f"""
        MATCH (pr:{self.pull_request_label} {{id: $id, organisation_id: $organisation_id}})
        RETURN pr
        """
    
    @property
    def LIST_PULL_REQUESTS_BY_REPOSITORY(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MATCH (pr:{self.pull_request_label})-[:BELONGS_TO_REPO]->(r)
        RETURN pr
        ORDER BY pr.created_at DESC
        LIMIT $limit
        """
    
    @property
    def CREATE_PULL_REQUEST_CREATOR_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (pr:{self.pull_request_label} {{id: $pull_request_id, organisation_id: $organisation_id}})
        MERGE (u)-[rel:CREATES_PULL_REQUEST]->(pr)
        SET rel.created_at = $created_at
        RETURN rel
        """
    
    @property
    def CREATE_PULL_REQUEST_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (pr:{self.pull_request_label} {{id: $pull_request_id, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (pr)-[rel:BELONGS_TO_REPO]->(r)
        SET rel.created_at = $created_at
        RETURN rel
        """


class GitHubCommitQueries(GitHubBaseQueries):
    """Queries for GitHub commit operations."""
    
    @property
    def CREATE_COMMIT(self):
        return f"""
        MERGE (c:{self.commit_label} {{sha: $sha}})
        SET c.organisation_id = $organisation_id,
            c.node_id = $node_id,
            c.html_url = $html_url,
            c.message = $message,
            c.additions = $additions,
            c.deletions = $deletions,
            c.total = $total,
            c.created_at = $created_at
        RETURN c
        """
    
    @property
    def GET_COMMIT_BY_SHA(self):
        return f"""
        MATCH (c:{self.commit_label} {{sha: $sha, organisation_id: $organisation_id}})
        RETURN c
        """
    
    @property
    def LIST_COMMITS_BY_REPOSITORY(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MATCH (c:{self.commit_label})-[:BELONGS_TO_REPO]->(r)
        RETURN c
        ORDER BY c.created_at DESC
        LIMIT $limit
        """
    
    @property
    def CREATE_COMMIT_AUTHOR_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (c:{self.commit_label} {{sha: $commit_sha, organisation_id: $organisation_id}})
        MERGE (u)-[rel:AUTHORS_COMMIT]->(c)
        SET rel.authored_at = $authored_at
        RETURN rel
        """
    
    @property
    def CREATE_COMMIT_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (c:{self.commit_label} {{sha: $commit_sha, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (c)-[rel:BELONGS_TO_REPO]->(r)
        SET rel.created_at = $created_at
        RETURN rel
        """


class GitHubFileQueries(GitHubBaseQueries):
    """Queries for GitHub file operations."""
    
    @property
    def CREATE_FILE(self):
        return f"""
        MERGE (f:{self.file_label} {{path: $path, organisation_id: $organisation_id}})
        SET f.name = $name,
            f.sha = $sha,
            f.size = $size,
            f.html_url = $html_url,
            f.download_url = $download_url,
            f.type = $type,
            f.content = $content,
            f.encoding = $encoding,
            f.vector_id = $vector_id,
            f.vectorized_at = $vectorized_at,
            f.content_hash = $content_hash,
            f.last_processed = $last_processed,
            f.processing_status = $processing_status
        RETURN f
        """
    
    @property
    def GET_FILE_BY_PATH(self):
        return f"""
        MATCH (f:{self.file_label} {{path: $path, organisation_id: $organisation_id}})
        RETURN f
        """
    
    @property
    def LIST_FILES_BY_REPOSITORY(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MATCH (f:{self.file_label})-[:BELONGS_TO_REPO]->(r)
        RETURN f
        ORDER BY f.path
        LIMIT $limit
        """
    
    @property
    def CREATE_FILE_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (f:{self.file_label} {{path: $file_path, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (r)-[rel:CONTAINS_FILE]->(f)
        SET rel.added_at = $added_at
        RETURN rel
        """
    
    @property
    def CREATE_COMMIT_FILE_RELATIONSHIP(self):
        return f"""
        MATCH (c:{self.commit_label} {{sha: $commit_sha, organisation_id: $organisation_id}})
        MATCH (f:{self.file_label} {{path: $file_path, organisation_id: $organisation_id}})
        MERGE (c)-[rel:MODIFIES_FILE]->(f)
        SET rel.additions = $additions,
            rel.deletions = $deletions,
            rel.status = $status
        RETURN rel
        """


class GitHubSyncQueries(GitHubBaseQueries):
    """Queries for GitHub sync operations."""
    
    @property
    def GET_LAST_SYNC_TIME(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        RETURN o.github_last_sync_time as last_sync_time
        """
    
    @property
    def UPDATE_LAST_SYNC_TIME(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        SET o.github_last_sync_time = $sync_time
        RETURN o
        """
    
    @property
    def GET_SYNC_STATISTICS(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        OPTIONAL MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (i:{self.issue_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (pr:{self.pull_request_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (c:{self.commit_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        RETURN 
            count(DISTINCT r) as repositories_count,
            count(DISTINCT i) as issues_count,
            count(DISTINCT pr) as pull_requests_count,
            count(DISTINCT c) as commits_count,
            count(DISTINCT f) as files_count
        """


class GitHubRelationshipQueries(GitHubBaseQueries):
    """Queries for creating relationships between GitHub entities and organizational entities."""

    @property
    def CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        MERGE (o)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = 'owner',
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (d:{self.department_label} {{organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        WHERE d.organisation_id = r.organisation_id
        MERGE (d)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = 'department',
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def CREATE_USER_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_label} {{organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        WHERE u.organisation_id = r.organisation_id
        MERGE (u)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = $access_type,
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def MAP_USER_REPOSITORY_ACCESS(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (o)-[:HAS_DEPARTMENT]->(d:{self.department_label})
        MATCH (d)<-[:BELONGS_TO]-(u:{self.user_label})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (u)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = 'member',
            rel.granted_at = $granted_at
        RETURN count(rel) as relationships_created
        """

    @property
    def MAP_GENERAL_DEPARTMENT_ACCESS(self):
        return f"""
        MATCH (d:{self.department_label} {{organisation_id: $organisation_id, name: 'GENERAL'}})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (d)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = 'department',
            rel.granted_at = $granted_at
        RETURN count(rel) as relationships_created
        """

    @property
    def CREATE_AGENT_REPOSITORY_RELATIONSHIP(self):
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[:BELONGS_TO]->(d:{self.department_label})
        MATCH (r:{self.repository_label} {{id: $repository_id}})
        WHERE d.organisation_id = r.organisation_id
        MERGE (d)-[rel:{self.has_access_rel}]->(r)
        SET rel.access_type = 'agent_access',
            rel.granted_at = $granted_at
        RETURN d.id as department_id
        """

    @property
    def CREATE_USER_GITHUB_USER_MAPPING(self):
        return f"""
        MATCH (u:{self.user_label} {{email: $email, organisation_id: $organisation_id}})
        MATCH (gu:{self.user_github_label} {{login: $github_login, organisation_id: $organisation_id}})
        MERGE (u)-[rel:MAPPED_TO]->(gu)
        SET rel.mapped_at = $mapped_at,
            rel.mapping_type = 'email_login'
        RETURN rel
        """

    @property
    def GET_USER_GITHUB_MAPPING(self):
        return f"""
        MATCH (u:{self.user_label} {{organisation_id: $organisation_id}})-[rel:MAPPED_TO]->(gu:{self.user_github_label})
        RETURN u, gu, rel
        """

    @property
    def CREATE_ORGANISATION_ISSUE_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (i:{self.issue_label} {{id: $issue_id, organisation_id: $organisation_id}})
        MERGE (o)-[rel:{self.has_access_rel}]->(i)
        SET rel.access_type = 'owner',
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def CREATE_ORGANISATION_PULL_REQUEST_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (pr:{self.pull_request_label} {{id: $pull_request_id, organisation_id: $organisation_id}})
        MERGE (o)-[rel:{self.has_access_rel}]->(pr)
        SET rel.access_type = 'owner',
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def CREATE_ORGANISATION_COMMIT_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (c:{self.commit_label} {{sha: $commit_sha, organisation_id: $organisation_id}})
        MERGE (o)-[rel:{self.has_access_rel}]->(c)
        SET rel.access_type = 'owner',
            rel.granted_at = $granted_at
        RETURN rel
        """

    @property
    def CREATE_ORGANISATION_FILE_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        MERGE (o)-[rel:{self.has_access_rel}]->(f)
        SET rel.access_type = 'owner',
            rel.granted_at = $granted_at
        RETURN count(rel) as relationships_created
        """


class GitHubMetadataQueries(GitHubBaseQueries):
    """Queries for GitHub metadata operations."""
    
    @property
    def GET_REPOSITORY_STATISTICS(self):
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        OPTIONAL MATCH (r)<-[:BELONGS_TO_REPO]-(i:{self.issue_label})
        OPTIONAL MATCH (r)<-[:BELONGS_TO_REPO]-(pr:{self.pull_request_label})
        OPTIONAL MATCH (r)<-[:BELONGS_TO_REPO]-(c:{self.commit_label})
        OPTIONAL MATCH (r)-[:CONTAINS_FILE]->(f:{self.file_label})
        RETURN 
            r,
            count(DISTINCT i) as issues_count,
            count(DISTINCT pr) as pull_requests_count,
            count(DISTINCT c) as commits_count,
            count(DISTINCT f) as files_count
        """
    
    @property
    def GET_USER_ACTIVITY_SUMMARY(self):
        return f"""
        MATCH (u:{self.user_github_label} {{id: $user_id, organisation_id: $organisation_id}})
        OPTIONAL MATCH (u)-[:OWNS_REPOSITORY]->(r:{self.repository_label})
        OPTIONAL MATCH (u)-[:CREATES_ISSUE]->(i:{self.issue_label})
        OPTIONAL MATCH (u)-[:CREATES_PULL_REQUEST]->(pr:{self.pull_request_label})
        OPTIONAL MATCH (u)-[:AUTHORS_COMMIT]->(c:{self.commit_label})
        RETURN
            u,
            count(DISTINCT r) as owned_repositories,
            count(DISTINCT i) as created_issues,
            count(DISTINCT pr) as created_pull_requests,
            count(DISTINCT c) as authored_commits
        """


class GitHubOrganizationalQueries(GitHubBaseQueries):
    """Queries for GitHub organizational integration following GDrive patterns."""

    @property
    def CREATE_ALL_ORGANIZATIONAL_RELATIONSHIPS(self):
        return f"""
        // Create organisation-repository relationships
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (o)-[rel1:{self.has_access_rel}]->(r)
        SET rel1.access_type = 'owner',
            rel1.granted_at = $granted_at

        // Create department-repository relationships
        WITH o
        MATCH (o)-[:HAS_DEPARTMENT]->(d:{self.department_label})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (d)-[rel2:{self.has_access_rel}]->(r)
        SET rel2.access_type = 'department',
            rel2.granted_at = $granted_at

        // Create user-repository relationships through departments
        WITH d
        MATCH (d)<-[:BELONGS_TO]-(u:{self.user_label})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (u)-[rel3:{self.has_access_rel}]->(r)
        SET rel3.access_type = 'member',
            rel3.granted_at = $granted_at

        RETURN count(DISTINCT rel1) as org_relationships,
               count(DISTINCT rel2) as dept_relationships,
               count(DISTINCT rel3) as user_relationships
        """

    @property
    def MAP_GITHUB_USERS_TO_ORG_USERS(self):
        return f"""
        MATCH (u:{self.user_label} {{organisation_id: $organisation_id}})
        MATCH (gu:{self.user_github_label} {{organisation_id: $organisation_id}})
        WHERE u.email IS NOT NULL AND gu.email IS NOT NULL AND u.email = gu.email
        MERGE (u)-[rel:MAPPED_TO]->(gu)
        SET rel.mapped_at = $mapped_at,
            rel.mapping_type = 'email_match'
        RETURN count(rel) as mappings_created
        """

    @property
    def GET_USER_ACCESSIBLE_REPOSITORIES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (u)-[:{self.has_access_rel}]->(r:{self.repository_label})
        RETURN r
        ORDER BY r.updated_at DESC
        LIMIT $limit
        """

    @property
    def GET_DEPARTMENT_ACCESSIBLE_REPOSITORIES(self):
        return f"""
        MATCH (d:{self.department_label} {{id: $department_id, organisation_id: $organisation_id}})
        MATCH (d)-[:{self.has_access_rel}]->(r:{self.repository_label})
        RETURN r
        ORDER BY r.updated_at DESC
        LIMIT $limit
        """

    @property
    def CHECK_USER_REPOSITORY_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id, organisation_id: $organisation_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id, organisation_id: $organisation_id}})
        OPTIONAL MATCH (u)-[direct_access:{self.has_access_rel}]->(r)
        OPTIONAL MATCH (u)-[:BELONGS_TO]->(d:{self.department_label})-[dept_access:{self.has_access_rel}]->(r)
        OPTIONAL MATCH (o:{self.organisation_label} {{id: $organisation_id}})-[org_access:{self.has_access_rel}]->(r)
        RETURN
            CASE
                WHEN direct_access IS NOT NULL THEN true
                WHEN dept_access IS NOT NULL THEN true
                WHEN org_access IS NOT NULL THEN true
                ELSE false
            END as has_access,
            direct_access.access_type as direct_access_type,
            dept_access.access_type as department_access_type,
            org_access.access_type as organization_access_type
        """

    @property
    def SYNC_ORGANIZATIONAL_ACCESS_PATTERNS(self):
        return f"""
        // Ensure all repositories have organizational access
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (o)-[org_rel:{self.has_access_rel}]->(r)
        SET org_rel.access_type = 'owner',
            org_rel.granted_at = $granted_at

        // Ensure GENERAL department has access to all repositories
        WITH o
        MATCH (o)-[:HAS_DEPARTMENT]->(general:{self.department_label} {{name: 'GENERAL'}})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (general)-[dept_rel:{self.has_access_rel}]->(r)
        SET dept_rel.access_type = 'department',
            dept_rel.granted_at = $granted_at

        // Ensure all users in the organization have access through their departments
        WITH general
        MATCH (general)<-[:BELONGS_TO]-(u:{self.user_label})
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        MERGE (u)-[user_rel:{self.has_access_rel}]->(r)
        SET user_rel.access_type = 'member',
            user_rel.granted_at = $granted_at

        RETURN count(DISTINCT org_rel) as org_access_count,
               count(DISTINCT dept_rel) as dept_access_count,
               count(DISTINCT user_rel) as user_access_count
        """
